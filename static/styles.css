* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.main {
    display: flex;
    gap: 20px;
}

.chat-container {
    flex: 2;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 70vh;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.message {
    margin-bottom: 15px;
    display: flex;
}

.message.user {
    justify-content: flex-end;
}

.message-content {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user .message-content {
    background-color: #3498db;
    color: white;
    border-bottom-right-radius: 4px;
}

.bot .message-content {
    background-color: #f1f1f1;
    border-bottom-left-radius: 4px;
}

.message-metadata {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.chat-input {
    display: flex;
    padding: 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
}

.chat-input input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

.chat-input button {
    margin-left: 10px;
    padding: 0 20px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
}

.chat-input button:hover {
    background-color: #2980b9;
}

.admin-panel {
    flex: 1;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.admin-panel h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.panel-section {
    margin-bottom: 25px;
}

.panel-section h3 {
    margin-bottom: 10px;
    color: #34495e;
}

.panel-section p {
    margin-bottom: 10px;
    color: #7f8c8d;
}

textarea {
    width: 100%;
    height: 100px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
    font-family: inherit;
    resize: vertical;
}

button {
    padding: 10px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

button:hover {
    background-color: #2980b9;
}

#status-container {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 10px;
}

.source-tag {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-top: 5px;
}

.source-support {
    background-color: #2ecc71;
    color: white;
}

.source-crawled {
    background-color: #f39c12;
    color: white;
}

.source-ai {
    background-color: #9b59b6;
    color: white;
}

.sources-list {
    margin-top: 8px;
    font-size: 12px;
}

.sources-list a {
    color: #3498db;
    text-decoration: none;
}

.sources-list a:hover {
    text-decoration: underline;
}

.loading {
    opacity: 0.6;
}