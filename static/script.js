document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');
    const urlsInput = document.getElementById('urls-input');
    const crawlButton = document.getElementById('crawl-button');
    const crawlWaitlistButton = document.getElementById('crawl-waitlist-button');
    const statusContainer = document.getElementById('status-container');
    const refreshStatusButton = document.getElementById('refresh-status');

    // Initial status check
    fetchStatus();

    // Event listeners
    sendButton.addEventListener('click', sendMessage);
    userInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });

    crawlButton.addEventListener('click', crawlWebsites);
    crawlWaitlistButton.addEventListener('click', crawlWaitlistSupport);
    refreshStatusButton.addEventListener('click', fetchStatus);

    // Send user message
    function sendMessage() {
        const message = userInput.value.trim();
        if (!message) return;

        // Add user message to chat
        addMessage(message, 'user');

        // Clear input
        userInput.value = '';

        // Show loading indicator
        const loadingId = addMessage('Thinking...', 'bot', true);

        // Send to API
        fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ message })
        })
        .then(response => response.json())
        .then(data => {
            // Remove loading message
            removeMessage(loadingId);

            if (data.success) {
                // Add bot response with source information
                addBotResponse(data);
            } else {
                addMessage('Sorry, I encountered an error: ' + data.error, 'bot');
            }
        })
        .catch(error => {
            // Remove loading message
            removeMessage(loadingId);
            addMessage('Sorry, there was an error processing your request.', 'bot');
            console.error('Error:', error);
        });
    }

    // Add message to chat
    function addMessage(content, sender, isLoading = false) {
        const messageId = 'msg-' + Date.now();
        const messageDiv = document.createElement('div');
        messageDiv.id = messageId;
        messageDiv.className = `message ${sender}`;
        if (isLoading) messageDiv.classList.add('loading');

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = content;

        messageDiv.appendChild(messageContent);
        chatMessages.appendChild(messageDiv);

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;

        return messageId;
    }

    // Remove message by ID
    function removeMessage(messageId) {
        const message = document.getElementById(messageId);
        if (message) {
            message.remove();
        }
    }

    // Add bot response with source information
    function addBotResponse(data) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message bot';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = data.response;

        // Add source tag
        const sourceTag = document.createElement('span');
        sourceTag.className = 'source-tag';

        if (data.source === 'csv_support_data') {
            sourceTag.textContent = 'Support CSV (Exact Match)';
            sourceTag.classList.add('source-support');

            // Add metadata
            if (data.metadata && data.metadata.question) {
                const metadataDiv = document.createElement('div');
                metadataDiv.className = 'message-metadata';
                metadataDiv.textContent = `Matched: "${data.metadata.question}"`;
                messageContent.appendChild(metadataDiv);
            }
        } else if (data.source === 'crawled_content') {
            sourceTag.textContent = 'Website Content';
            sourceTag.classList.add('source-crawled');

            // Add sources
            if (data.metadata && data.metadata.sources && data.metadata.sources.length > 0) {
                const sourcesDiv = document.createElement('div');
                sourcesDiv.className = 'sources-list';
                sourcesDiv.innerHTML = '<strong>Sources:</strong><br>';

                data.metadata.sources.forEach(source => {
                    const sourceLink = document.createElement('a');
                    sourceLink.href = source.url;
                    sourceLink.target = '_blank';
                    sourceLink.textContent = source.title || source.url;
                    sourcesDiv.appendChild(sourceLink);
                    sourcesDiv.appendChild(document.createElement('br'));
                });

                messageContent.appendChild(sourcesDiv);
            }
        } else {
            sourceTag.textContent = 'AI Generated';
            sourceTag.classList.add('source-ai');
        }

        messageContent.appendChild(sourceTag);
        messageDiv.appendChild(messageContent);
        chatMessages.appendChild(messageDiv);

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Crawl websites
    function crawlWebsites() {
        const urlsText = urlsInput.value.trim();
        if (!urlsText) {
            alert('Please enter at least one URL to crawl');
            return;
        }

        // Parse URLs (one per line)
        const urls = urlsText.split('\n')
            .map(url => url.trim())
            .filter(url => url.length > 0);

        if (urls.length === 0) {
            alert('Please enter valid URLs');
            return;
        }

        // Disable button and show loading
        crawlButton.disabled = true;
        crawlButton.textContent = 'Crawling...';

        // Send to API
        fetch('/api/crawl', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ urls })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Crawling completed! Processed ${data.stats.crawled_urls} URLs with ${data.stats.total_chunks} content chunks.`);
                // Refresh status
                fetchStatus();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error crawling websites. Check console for details.');
            console.error('Error:', error);
        })
        .finally(() => {
            // Re-enable button
            crawlButton.disabled = false;
            crawlButton.textContent = 'Crawl Websites';
        });
    }

    // Crawl waitlist.me support URLs
    function crawlWaitlistSupport() {
        // Disable button and show loading
        crawlWaitlistButton.disabled = true;
        crawlWaitlistButton.textContent = 'Crawling Waitlist.me...';

        // Send to API
        fetch('/api/crawl-waitlist', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ max_urls: 20 })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Crawling completed! Found ${data.urls_found} URLs, crawled ${data.urls_crawled} URLs with ${data.stats.total_chunks} content chunks.`);
                // Refresh status
                fetchStatus();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error crawling waitlist.me support. Check console for details.');
            console.error('Error:', error);
        })
        .finally(() => {
            // Re-enable button
            crawlWaitlistButton.disabled = false;
            crawlWaitlistButton.textContent = 'Crawl Waitlist.me Support';
        });
    }

    // Fetch system status
    function fetchStatus() {
        statusContainer.innerHTML = '<p>Loading status...</p>';

        fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                let statusHtml = `
                    <div>
                        <p><strong>System Status:</strong> ${data.status}</p>
                        <p><strong>Support Data:</strong> ${data.support_data.loaded ? 'Loaded' : 'Not loaded'} (${data.support_data.count} questions)</p>
                        <p><strong>Embedding Model:</strong> ${data.embedding_model.loaded ? 'Loaded' : 'Not loaded'}</p>
                        <p><strong>Crawled Content:</strong> ${data.crawled_content.loaded ? 'Available' : 'None'} (${data.crawled_content.count} chunks)</p>
                    </div>
                `;

                statusContainer.innerHTML = statusHtml;
            })
            .catch(error => {
                statusContainer.innerHTML = '<p>Error fetching status</p>';
                console.error('Error:', error);
            });
    }
});