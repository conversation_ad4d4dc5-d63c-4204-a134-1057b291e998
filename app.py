import os
import json
import logging
import numpy as np
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import pandas as pd
from dotenv import load_dotenv
from google.cloud import aiplatform
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Initialize global variables
support_data = None
embedding_model = None
support_embeddings = None
crawled_content = []
crawled_embeddings = None

# Load support data from CSV
def load_support_data():
    global support_data
    try:
        csv_path = os.path.join(os.path.dirname(__file__), 'data', 'support.csv')
        support_data = pd.read_csv(csv_path)
        logger.info(f"Loaded {len(support_data)} support questions")
        return True
    except Exception as e:
        logger.error(f"Error loading support data: {e}")
        return False

# Initialize embedding model
def init_embedding_model():
    global embedding_model
    try:
        # Using sentence-transformers for embeddings
        model_name = "all-MiniLM-L6-v2"  # Small but effective model
        embedding_model = SentenceTransformer(model_name)
        logger.info(f"Loaded embedding model: {model_name}")
        return True
    except Exception as e:
        logger.error(f"Error loading embedding model: {e}")
        return False

# Generate embeddings for support questions
def generate_support_embeddings():
    global support_embeddings
    try:
        if support_data is None or embedding_model is None:
            return False

        # Generate embeddings for all questions
        questions = support_data['Question'].tolist()
        support_embeddings = embedding_model.encode(questions)
        logger.info(f"Generated embeddings for {len(questions)} questions")
        return True
    except Exception as e:
        logger.error(f"Error generating support embeddings: {e}")
        return False

# Find relevant support question for a query
def find_relevant_support_topic(query, similarity_threshold=0.6):
    try:
        if support_embeddings is None or embedding_model is None:
            return None

        # Generate embedding for the query
        query_embedding = embedding_model.encode([query])[0]

        # Calculate similarities with all support questions
        similarities = cosine_similarity([query_embedding], support_embeddings)[0]

        # Find the best match
        best_match_idx = np.argmax(similarities)
        best_match_score = similarities[best_match_idx]

        logger.info(f"Best match score: {best_match_score:.4f} for question: {support_data['Question'].iloc[best_match_idx][:30]}...")

        # Return the best match if it's above the threshold
        if best_match_score >= similarity_threshold:
            return {
                'question': support_data['Question'].iloc[best_match_idx],
                'answer': support_data['Answer'].iloc[best_match_idx],
                'similarity': float(best_match_score)
            }

        return None
    except Exception as e:
        logger.error(f"Error finding relevant support question: {e}")
        return None

# Find relevant content from crawled website data
def find_relevant_crawled_content(query, similarity_threshold=0.5, max_results=3):
    try:
        if not crawled_content or crawled_embeddings is None or embedding_model is None:
            return None

        # Generate embedding for the query
        query_embedding = embedding_model.encode([query])[0]

        # Calculate similarities with all crawled content
        similarities = cosine_similarity([query_embedding], crawled_embeddings)[0]

        # Find the best matches
        top_indices = similarities.argsort()[-max_results:][::-1]

        # Filter by threshold and prepare results
        relevant_content = []
        for idx in top_indices:
            if similarities[idx] >= similarity_threshold:
                relevant_content.append({
                    'content': crawled_content[idx]['content'],
                    'url': crawled_content[idx]['url'],
                    'title': crawled_content[idx]['title'],
                    'similarity': float(similarities[idx])
                })

        if relevant_content:
            logger.info(f"Found {len(relevant_content)} relevant crawled content pieces")
            return relevant_content

        return None
    except Exception as e:
        logger.error(f"Error finding relevant crawled content: {e}")
        return None

# Generate response using Vertex AI with context
def generate_ai_response(query, context=None):
    try:
        # Check if Vertex AI is configured
        project_id = os.environ.get('GOOGLE_CLOUD_PROJECT')
        if not project_id:
            # Fallback response if Vertex AI is not configured
            return "I don't have enough information to answer that question. (Note: Vertex AI is not configured)"

        # Initialize Vertex AI
        aiplatform.init(
            project=project_id,
            location=os.environ.get('GOOGLE_CLOUD_LOCATION', 'us-central1')
        )

        # Get model parameters
        model_name = "gemini-2.0-flash-001"
        temperature = 0.7
        max_output_tokens = 1024
        top_p = 0.95
        top_k = 40

        # Create model instance
        model = aiplatform.GenerativeModel(model_name=model_name)

        # Prepare prompt with context if available
        if context:
            context_text = "\n\n".join([f"Source: {item['url']}\nTitle: {item['title']}\nContent: {item['content']}"
                                       for item in context])

            prompt = f"""You are a helpful assistant. Answer the user's question based on the following context information.
            If the context doesn't contain relevant information, say that you don't have enough information.

            Context:
            {context_text}

            User Question: {query}

            Answer:"""
        else:
            prompt = f"""You are a helpful assistant. Answer the user's question to the best of your ability.
            If you don't know the answer, say so.

            User Question: {query}

            Answer:"""

        # Generate content
        response = model.generate_content(
            prompt,
            generation_config={
                "temperature": temperature,
                "max_output_tokens": max_output_tokens,
                "top_p": top_p,
                "top_k": top_k
            }
        )

        # Extract and return the text response
        return response.text
    except Exception as e:
        logger.error(f"Error generating AI response: {e}")
        return f"I encountered an error while processing your question. (Error: {str(e)})"

# Extract text content from HTML
def extract_text_from_html(html):
    try:
        soup = BeautifulSoup(html, 'html.parser')

        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header"]):
            script.extract()

        # Get text
        text = soup.get_text()

        # Clean up text
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = '\n'.join(chunk for chunk in chunks if chunk)

        return text
    except Exception as e:
        logger.error(f"Error extracting text from HTML: {e}")
        return ""

# Extract metadata from HTML
def extract_metadata(html, url):
    try:
        soup = BeautifulSoup(html, 'html.parser')

        title = soup.title.string if soup.title else ""
        description = ""
        meta_desc = soup.find("meta", {"name": "description"})
        if meta_desc:
            description = meta_desc.get("content", "")

        return {
            "title": title.strip() if title else "",
            "description": description.strip(),
            "url": url
        }
    except Exception as e:
        logger.error(f"Error extracting metadata: {e}")
        return {"title": "", "description": "", "url": url}

# Chunk text into smaller pieces
def chunk_text(text, max_chunk_size=1000, overlap=200):
    if not text or len(text) <= max_chunk_size:
        return [text]

    chunks = []
    start = 0

    while start < len(text):
        end = start + max_chunk_size

        if end < len(text):
            # Try to break at sentence or paragraph
            for separator in ['\n\n', '\n', '. ', '? ', '! ']:
                last_separator = text.rfind(separator, start, end)
                if last_separator > start + (max_chunk_size // 2):
                    end = last_separator + len(separator)
                    break

        chunks.append(text[start:end].strip())
        start = end - overlap

    return [chunk for chunk in chunks if len(chunk) > 50]

# Crawl a single URL
def crawl_url(url):
    try:
        logger.info(f"Crawling URL: {url}")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        html = response.text
        text = extract_text_from_html(html)
        metadata = extract_metadata(html, url)

        # Chunk the text
        chunks = chunk_text(text)

        result = []
        for i, chunk in enumerate(chunks):
            result.append({
                "url": url,
                "title": metadata["title"],
                "description": metadata["description"],
                "content": chunk,
                "chunk_id": f"{url}_{i}"
            })

        logger.info(f"Extracted {len(chunks)} chunks from {url}")
        return result
    except Exception as e:
        logger.error(f"Error crawling URL {url}: {e}")
        return []

# Extract all URLs from waitlist.me support page
def extract_waitlist_support_urls():
    try:
        base_url = "https://www.waitlist.me/support/"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(base_url, headers=headers, timeout=10)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        # Find all links in the support page
        links = soup.find_all('a', href=True)

        urls = []
        for link in links:
            href = link['href']
            # Convert relative URLs to absolute URLs
            if href.startswith('/'):
                full_url = urljoin("https://www.waitlist.me", href)
            elif href.startswith('http'):
                full_url = href
            else:
                continue

            # Only include waitlist.me URLs that are support-related
            if 'waitlist.me' in full_url and ('/support/' in full_url or '/features/' in full_url):
                urls.append(full_url)

        # Remove duplicates and sort
        unique_urls = list(set(urls))
        unique_urls.sort()

        logger.info(f"Extracted {len(unique_urls)} unique URLs from waitlist.me support page")
        return unique_urls

    except Exception as e:
        logger.error(f"Error extracting waitlist.me support URLs: {e}")
        return []

# Crawl multiple URLs
def crawl_urls(urls, max_urls=10):
    global crawled_content, crawled_embeddings

    all_chunks = []
    count = 0

    for url in urls[:max_urls]:
        if count >= max_urls:
            break

        chunks = crawl_url(url)
        all_chunks.extend(chunks)
        count += 1

        # Be nice to servers
        time.sleep(1)

    # Store the crawled content
    crawled_content = all_chunks

    # Generate embeddings for all chunks
    if embedding_model and all_chunks:
        try:
            texts = [chunk["content"] for chunk in all_chunks]
            crawled_embeddings = embedding_model.encode(texts)
            logger.info(f"Generated embeddings for {len(texts)} crawled chunks")
        except Exception as e:
            logger.error(f"Error generating embeddings for crawled content: {e}")

    return {
        "success": True,
        "crawled_urls": count,
        "total_chunks": len(all_chunks)
    }

# Save crawled data to file
def save_crawled_data(filename="data/crawled_data.json"):
    try:
        if not crawled_content:
            logger.warning("No crawled content to save")
            return False

        # Ensure directory exists
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        # Convert data to JSON
        data_json = json.dumps({
            "crawled_at": time.time(),
            "content": crawled_content
        })

        # Save to file
        with open(filename, 'w') as f:
            f.write(data_json)

        logger.info(f"Saved crawled data to {filename}")
        return True
    except Exception as e:
        logger.error(f"Error saving crawled data: {e}")
        return False

# Load crawled data from file
def load_crawled_data(filename="data/crawled_data.json"):
    global crawled_content, crawled_embeddings

    try:
        if not os.path.exists(filename):
            logger.warning(f"File {filename} does not exist")
            return False

        # Load from file
        with open(filename, 'r') as f:
            data = json.load(f)

        crawled_content = data.get("content", [])

        # Generate embeddings for loaded content
        if embedding_model and crawled_content:
            texts = [chunk["content"] for chunk in crawled_content]
            crawled_embeddings = embedding_model.encode(texts)

        logger.info(f"Loaded {len(crawled_content)} crawled chunks from file")
        return True
    except Exception as e:
        logger.error(f"Error loading crawled data: {e}")
        return False

# Initialize the application
def initialize_app():
    # Create data directory if it doesn't exist
    os.makedirs('data', exist_ok=True)

    # Load support data from existing support.csv
    load_support_data()

    # Initialize embedding model
    init_embedding_model()

    # Generate support embeddings
    if support_data is not None and embedding_model is not None:
        generate_support_embeddings()

    # Try to load previously crawled data
    load_crawled_data()

    # Auto-crawl waitlist.me support URLs if no crawled data exists
    if len(crawled_content) == 0:
        logger.info("No crawled data found, auto-crawling waitlist.me support URLs...")
        try:
            waitlist_urls = extract_waitlist_support_urls()
            if waitlist_urls:
                # Crawl up to 20 URLs from waitlist.me
                crawl_result = crawl_urls(waitlist_urls, max_urls=20)
                save_crawled_data()
                logger.info(f"Auto-crawled {crawl_result['crawled_urls']} URLs with {crawl_result['total_chunks']} chunks")
        except Exception as e:
            logger.error(f"Error during auto-crawling: {e}")

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/static/<path:path>')
def serve_static(path):
    return send_from_directory('static', path)

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        # Get request data
        data = request.get_json()

        if not data or 'message' not in data:
            return jsonify({
                'success': False,
                'error': 'Message is required'
            }), 400

        user_message = data['message']
        logger.info(f"Received message: {user_message[:50]}...")

        # Step 1: Check if this is a support-related query (CSV data - word for word)
        support_topic = find_relevant_support_topic(user_message)

        if support_topic:
            logger.info(f"Found matching support question with similarity {support_topic['similarity']:.4f}")
            return jsonify({
                'success': True,
                'response': support_topic['answer'],  # Word-for-word from CSV
                'source': 'csv_support_data',
                'metadata': {
                    'question': support_topic['question'],
                    'similarity': support_topic['similarity']
                }
            })

        # Step 2: If not a support query, check crawled content
        relevant_content = find_relevant_crawled_content(user_message)

        if relevant_content:
            # Use Vertex AI with context from crawled content
            logger.info("Using crawled content as context for AI response")
            ai_response = generate_ai_response(user_message, relevant_content)

            return jsonify({
                'success': True,
                'response': ai_response,
                'source': 'crawled_content',
                'metadata': {
                    'sources': [
                        {'url': item['url'], 'title': item['title'], 'similarity': item['similarity']}
                        for item in relevant_content
                    ]
                }
            })

        # Step 3: Last resort - use Vertex AI without context
        logger.info("No matching support topic or crawled content, using Vertex AI without context")
        ai_response = generate_ai_response(user_message)

        return jsonify({
            'success': True,
            'response': ai_response,
            'source': 'ai_only'
        })

    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
@app.route('/api/crawl', methods=['POST'])
def crawl():
    try:
        # Get request data
        data = request.get_json()

        if not data or 'urls' not in data or not data['urls']:
            return jsonify({
                'success': False,
                'error': 'URLs are required'
            }), 400

        urls = data['urls']
        max_urls = data.get('max_urls', 10)

        # Validate URLs
        valid_urls = []
        for url in urls:
            try:
                parsed = urlparse(url)
                if parsed.scheme and parsed.netloc:
                    valid_urls.append(url)
            except:
                pass

        if not valid_urls:
            return jsonify({
                'success': False,
                'error': 'No valid URLs provided'
            }), 400

        # Crawl the URLs
        result = crawl_urls(valid_urls, max_urls)

        # Save crawled data
        save_crawled_data()

        return jsonify({
            'success': True,
            'message': 'Crawling completed',
            'stats': result
        })

    except Exception as e:
        logger.error(f"Error processing crawl request: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/crawl-waitlist', methods=['POST'])
def crawl_waitlist():
    try:
        # Extract waitlist.me support URLs
        waitlist_urls = extract_waitlist_support_urls()

        if not waitlist_urls:
            return jsonify({
                'success': False,
                'error': 'No waitlist.me support URLs found'
            }), 400

        # Get max_urls from request or default to 20
        data = request.get_json() or {}
        max_urls = data.get('max_urls', 20)

        # Crawl the URLs
        result = crawl_urls(waitlist_urls, max_urls)

        # Save crawled data
        save_crawled_data()

        return jsonify({
            'success': True,
            'message': f'Crawled {result["crawled_urls"]} waitlist.me support URLs',
            'stats': result,
            'urls_found': len(waitlist_urls),
            'urls_crawled': result["crawled_urls"]
        })

    except Exception as e:
        logger.error(f"Error processing waitlist crawl request: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/status', methods=['GET'])
def status():
    return jsonify({
        'status': 'healthy',
        'support_data': {
            'loaded': support_data is not None,
            'count': len(support_data) if support_data is not None else 0
        },
        'embedding_model': {
            'loaded': embedding_model is not None
        },
        'crawled_content': {
            'loaded': len(crawled_content) > 0,
            'count': len(crawled_content)
        }
    })

# Main entry point
if __name__ == '__main__':
    # Initialize the app
    initialize_app()

    # Start the server
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=True)